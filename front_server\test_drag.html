<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽功能测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        .chart {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .instructions {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 4px;
            border-left: 4px solid #1a73e8;
        }
        .instructions h3 {
            margin: 0 0 10px 0;
            color: #1a73e8;
        }
        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">ECharts 滑动条拖拽功能测试</div>
        <div id="chart" class="chart"></div>
        
        <div class="instructions">
            <h3>测试说明：</h3>
            <ul>
                <li>尝试拖拽滑动条左右两端的圆形手柄</li>
                <li>尝试拖拽滑动条中间的选中区域</li>
                <li>观察图表是否实时更新</li>
                <li>检查控制台是否有错误信息</li>
            </ul>
        </div>
    </div>

    <script>
        // 生成测试数据
        function generateData() {
            const data = [];
            const now = new Date();
            for (let i = 0; i < 200; i++) {
                const time = new Date(now.getTime() - (200 - i) * 60000);
                data.push([
                    time,
                    Math.sin(i * 0.1) * 30 + 50 + Math.random() * 20
                ]);
            }
            return data;
        }

        const testData = generateData();

        // 初始化图表
        const chart = echarts.init(document.getElementById('chart'));

        const option = {
            title: {
                text: '测试数据趋势',
                left: 'center',
                textStyle: { fontSize: 16, color: '#333' }
            },
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    const time = new Date(params[0].value[0]);
                    const value = params[0].value[1].toFixed(2);
                    return `时间: ${time.toLocaleTimeString()}<br/>值: ${value}`;
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'time',
                boundaryGap: false,
                axisLabel: {
                    formatter: function(value) {
                        const date = new Date(value);
                        return `${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
                    }
                }
            },
            yAxis: {
                type: 'value',
                name: '数值'
            },
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                },
                {
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                    borderColor: 'transparent',
                    fillerColor: 'rgba(26, 115, 232, 0.2)',
                    handleStyle: {
                        color: '#1a73e8',
                        borderColor: '#ffffff',
                        borderWidth: 2,
                        shadowBlur: 3,
                        shadowColor: 'rgba(26, 115, 232, 0.3)'
                    },
                    moveHandleStyle: {
                        color: 'rgba(26, 115, 232, 0.1)',
                        borderColor: 'rgba(26, 115, 232, 0.3)',
                        borderWidth: 1
                    },
                    emphasis: {
                        handleStyle: {
                            color: '#0d47a1',
                            borderWidth: 3,
                            shadowBlur: 5,
                            shadowColor: 'rgba(13, 71, 161, 0.5)'
                        },
                        moveHandleStyle: {
                            color: 'rgba(26, 115, 232, 0.2)',
                            borderColor: 'rgba(26, 115, 232, 0.5)'
                        }
                    },
                    textStyle: {
                        color: '#5f6368',
                        fontSize: 11
                    },
                    height: 25,
                    bottom: 8,
                    left: '3%',
                    right: '3%',
                    realtime: true,
                    zoomLock: false,
                    // 关键配置 - 确保拖拽功能
                    handleIcon: 'circle',
                    handleSize: '120%',
                    moveOnMouseMove: true,
                    moveOnMouseWheel: false,
                    brushSelect: false,
                    throttle: 100
                }
            ],
            series: [{
                name: '测试数据',
                type: 'line',
                smooth: true,
                data: testData,
                lineStyle: {
                    width: 2,
                    color: '#1a73e8'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(26, 115, 232, 0.3)' },
                        { offset: 1, color: 'rgba(26, 115, 232, 0.05)' }
                    ])
                },
                symbol: 'none'
            }]
        };

        // 设置图表选项
        chart.setOption(option);

        // 监听dataZoom事件
        chart.on('dataZoom', function(params) {
            console.log('DataZoom事件触发:', params);
            console.log('开始位置:', params.start, '结束位置:', params.end);
        });

        // 响应窗口大小变化
        window.addEventListener('resize', function() {
            chart.resize();
        });

        // 输出调试信息
        console.log('图表初始化完成');
        console.log('ECharts版本:', echarts.version);
        console.log('请尝试拖拽滑动条手柄');
    </script>
</body>
</html>
