import { adminApi, dataApi } from './index'
import { mockTerminals, mockAlerts, mockMetrics, mockStats } from './mock'

// 模拟延迟
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

// 终端相关API
export const terminalApi = {
  // 获取终端列表
  async getTerminals(params = {}) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      let filteredTerminals = [...mockTerminals]

      // 模拟过滤逻辑
      if (params.identityMac) {
        filteredTerminals = filteredTerminals.filter(t =>
          t.identityMac.toLowerCase().includes(params.identityMac.toLowerCase())
        )
      }
      if (params.hostname) {
        filteredTerminals = filteredTerminals.filter(t =>
          t.hostname.toLowerCase().includes(params.hostname.toLowerCase())
        )
      }
      if (params.status !== undefined && params.status !== '') {
        filteredTerminals = filteredTerminals.filter(t => t.status === Number(params.status))
      }
      if (params.tag) {
        filteredTerminals = filteredTerminals.filter(t => {
          const tags = t.tags || {}
          return Object.values(tags).some(value =>
            String(value).toLowerCase().includes(params.tag.toLowerCase())
          )
        })
      }

      return {
        code: 200,
        data: {
          list: filteredTerminals,
          total: filteredTerminals.length,
          page: params.page || 1,
          pageSize: params.pageSize || 10
        },
        message: 'success'
      }
    }

    // 真实API调用 - 使用管理服务
    return adminApi.get('/api/v1/admin/terminals', { params })
  },

  // 获取终端详情
  async getTerminalDetail(identityMac) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      const terminal = mockTerminals.find(t => t.identityMac === identityMac)
      if (!terminal) {
        throw new Error('终端不存在')
      }
      return {
        code: 200,
        data: terminal,
        message: 'success'
      }
    }

    // 真实API调用 - 使用管理服务
    return adminApi.get(`/api/v1/admin/terminals/${identityMac}`)
  },

  // 更新终端信息
  async updateTerminal(identityMac, data) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      const index = mockTerminals.findIndex(t => t.identityMac === identityMac)
      if (index === -1) {
        throw new Error('终端不存在')
      }

      mockTerminals[index] = { ...mockTerminals[index], ...data }
      return {
        code: 200,
        data: mockTerminals[index],
        message: '更新成功'
      }
    }

    // 真实API调用 - 使用管理服务
    return adminApi.put(`/api/v1/admin/terminals/${identityMac}`, data)
  },

  // 删除终端
  async deleteTerminal(identityMac) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      const index = mockTerminals.findIndex(t => t.identityMac === identityMac)
      if (index === -1) {
        throw new Error('终端不存在')
      }

      mockTerminals.splice(index, 1)
      return {
        code: 200,
        message: '删除成功'
      }
    }

    // 真实API调用 - 使用管理服务
    return adminApi.delete(`/api/v1/admin/terminals/${identityMac}`)
  },

  // 获取实时指标
  async getRealTimeMetrics(identityMac) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      const metrics = mockMetrics[identityMac]
      if (!metrics) {
        throw new Error('设备指标不存在')
      }
      return {
        code: 200,
        data: metrics,
        message: 'success'
      }
    }

    // 真实API调用 - 使用管理服务获取最新指标数据
    return adminApi.get(`/api/v1/admin/terminal/metrics/device/${identityMac}/latest`)
  },

  // 根据时间范围获取历史指标数据
  async getHistoryMetricsByTimeRange(identityMac, timeRange) {
    const now = new Date()
    let startTime, endTime
    let size = 10000 // 增加默认获取数据量，支持大时间范围

    // 根据时间范围计算开始和结束时间
    switch (timeRange) {
      case '最近10分钟':
        startTime = new Date(now.getTime() - 10 * 60 * 1000)
        endTime = now
        size = 1000 // 增加数据量限制
        break
      case '最近1小时':
        startTime = new Date(now.getTime() - 60 * 60 * 1000)
        endTime = now
        size = 2000 // 增加数据量限制
        break
      case '最近6小时':
        startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000)
        endTime = now
        size = 5000 // 增加数据量限制
        break
      case '今日全天':
        startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0)
        endTime = now
        size = 10000 // 增加数据量限制，支持全天数据
        break
      case '最近24小时':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        endTime = now
        size = 10000 // 增加数据量限制，支持24小时数据
        break
      default:
        // 默认最近1小时
        startTime = new Date(now.getTime() - 60 * 60 * 1000)
        endTime = now
        size = 2000 // 增加默认数据量限制
    }

    // 格式化时间为后端需要的格式
    const formatDateTime = (date) => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }

    const startTimeStr = formatDateTime(startTime)
    const endTimeStr = formatDateTime(endTime)

    console.log(`获取${timeRange}历史数据: ${identityMac}, 时间范围: ${startTimeStr} - ${endTimeStr}, 请求数据量: ${size}`)

    // 优先使用全量数据接口，避免分页限制
    try {
      console.log(`尝试使用全量数据接口获取${timeRange}数据...`)
      const fullResponse = await adminApi.get(`/api/v1/admin/terminal/metrics/device/${identityMac}/range/all`, {
        params: {
          startTime: startTimeStr,
          endTime: endTimeStr
        }
      })

      console.log(`${timeRange}全量数据API响应: 总数据量=${fullResponse.totalElements}, 获取数据量=${fullResponse.content?.length}`)
      return fullResponse

    } catch (error) {
      console.warn(`全量数据接口调用失败，回退到分页接口: ${error.message}`)

      // 回退到原有的分页接口
      const response = await adminApi.get(`/api/v1/admin/terminal/metrics/device/${identityMac}/range`, {
        params: {
          startTime: startTimeStr,
          endTime: endTimeStr,
          page: 0,
          size: size
        }
      })

      console.log(`${timeRange}分页数据API响应: 总数据量=${response.totalElements}, 当前页数据量=${response.content?.length}`)

      // 如果数据量很大且被分页了，尝试获取更多数据
      if (response.totalElements > response.content?.length && response.totalElements <= 10000) {
        console.log(`检测到数据被分页，尝试获取全部数据 (总数: ${response.totalElements})`)

        // 重新请求，使用总数据量作为size
        const fullResponse = await adminApi.get(`/api/v1/admin/terminal/metrics/device/${identityMac}/range`, {
          params: {
            startTime: startTimeStr,
            endTime: endTimeStr,
            page: 0,
            size: Math.min(response.totalElements, 10000) // 限制最大10000条
          }
        })

        console.log(`获取全部数据成功: ${fullResponse.content?.length} 条`)
        return fullResponse
      }

      return response
    }
  },

  // 获取终端指标（用于概览页面弹框）
  async getTerminalMetrics(identityMac) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      const metrics = mockMetrics[identityMac]
      if (!metrics) {
        throw new Error('设备指标不存在')
      }
      return {
        code: 200,
        data: metrics,
        message: 'success'
      }
    }

    // 真实API调用 - 使用管理服务获取最新指标数据
    const response = await adminApi.get(`/api/v1/admin/terminal/metrics/device/${identityMac}/latest`)

    // 统一返回格式，包装成与Mock数据一致的结构
    return {
      code: 200,
      data: response,
      message: 'success'
    }
  },

  // 根据指标ID获取指标详情（用于告警关联查询）
  async getMetricById(metricId) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      // Mock数据中没有指标ID，返回默认数据
      return {
        code: 200,
        data: mockMetrics['default'] || {},
        message: 'success'
      }
    }

    // 真实API调用 - 使用管理服务根据指标ID获取指标详情
    const response = await adminApi.get(`/api/v1/admin/terminal/metrics/metric/${metricId}`)

    // 统一返回格式，包装成与Mock数据一致的结构
    return {
      code: 200,
      data: response,
      message: 'success'
    }
  },

  // 获取历史指标
  async getHistoryMetrics(identityMac, params = {}) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      // 模拟历史数据
      const baseMetrics = mockMetrics[identityMac]
      if (!baseMetrics) {
        throw new Error('设备指标不存在')
      }

      const historyData = []
      for (let i = 0; i < 10; i++) {
        historyData.push({
          ...baseMetrics,
          timestamp: new Date(Date.now() - i * 5 * 60 * 1000).toISOString(),
          cpu: {
            ...baseMetrics.cpu,
            usage: Math.max(0, baseMetrics.cpu.usage + (Math.random() - 0.5) * 20)
          },
          memory: {
            ...baseMetrics.memory,
            usage: Math.max(0, baseMetrics.memory.usage + (Math.random() - 0.5) * 20)
          }
        })
      }

      return {
        code: 200,
        data: {
          list: historyData,
          total: historyData.length
        },
        message: 'success'
      }
    }

    // 真实API调用 - 使用管理服务，根据参数选择不同的接口
    if (params.startTime && params.endTime) {
      // 按时间范围查询
      return adminApi.get(`/api/v1/admin/terminal/metrics/device/${identityMac}/range`, {
        params: {
          startTime: params.startTime,
          endTime: params.endTime,
          page: params.page || 0,
          size: params.size || 10
        }
      })
    } else {
      // 分页查询所有历史数据
      return adminApi.get(`/api/v1/admin/terminal/metrics/device/${identityMac}`, {
        params: {
          page: params.page || 0,
          size: params.size || 10
        }
      })
    }
  },

  // 根据自定义时间范围获取历史指标数据
  async getHistoryMetricsByCustomTimeRange(identityMac, params) {
    const { startTime, endTime } = params

    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()

      // 生成自定义时间范围的Mock数据
      const start = new Date(startTime)
      const end = new Date(endTime)
      const mockData = []

      // 每5分钟生成一个数据点
      const interval = 5 * 60 * 1000 // 5分钟
      for (let time = start.getTime(); time <= end.getTime(); time += interval) {
        const currentTime = new Date(time)
        const timeHash = currentTime.getTime()
        const seed = timeHash % 1000000

        mockData.push({
          identityMac,
          hostname: 'ec_3568_25030031',
          metricTime: currentTime.toISOString(),
          receiveTime: currentTime.toISOString(),
          cpuTemp: 45 + (seed % 30),
          cpuPercent: 20 + (seed % 60),
          memoryPercent: 30 + (seed % 40),
          groupBps: (seed % 100) * 1024 * 1024, // 转换为字节
          diskUsage: [{
            device: '/dev/mmcblk0p6',
            percent: 40 + (seed % 30),
            total: 512 * 1024 * 1024 * 1024,
            used: Math.floor(512 * 1024 * 1024 * 1024 * (40 + seed % 30) / 100),
            free: Math.floor(512 * 1024 * 1024 * 1024 * (60 - seed % 30) / 100)
          }],
          temperatures: {
            'soc-thermal': 42 + (seed % 25),
            'gpu-thermal': 48 + (seed % 20)
          },
          cpuUsage: {
            user: (10 + seed % 20).toString(),
            sys: (5 + seed % 15).toString(),
            idle: (75 + seed % 20).toString()
          },
          memoryUsage: {
            total: 8 * 1024 * 1024 * 1024,
            used: Math.floor(8 * 1024 * 1024 * 1024 * (30 + seed % 40) / 100),
            available: Math.floor(8 * 1024 * 1024 * 1024 * (70 - seed % 40) / 100),
            percent: 30 + (seed % 40)
          },
          cdata: { count: seed % 50 },
          zdata: { count: seed % 30 },
          groupUsage: {
            buckets: [
              { bucket: 0, conn: seed % 10, pps: seed % 1000, bps: seed % 10000 }
            ]
          }
        })
      }

      return {
        content: mockData,
        totalElements: mockData.length,
        totalPages: 1,
        size: mockData.length,
        number: 0
      }
    }

    // 格式化时间为后端期望的格式
    const formatDateTime = (dateTime) => {
      const date = new Date(dateTime)
      return date.getFullYear() + '-' +
             String(date.getMonth() + 1).padStart(2, '0') + '-' +
             String(date.getDate()).padStart(2, '0') + ' ' +
             String(date.getHours()).padStart(2, '0') + ':' +
             String(date.getMinutes()).padStart(2, '0') + ':' +
             String(date.getSeconds()).padStart(2, '0')
    }

    const startTimeStr = formatDateTime(startTime)
    const endTimeStr = formatDateTime(endTime)

    console.log(`获取自定义时间范围历史数据: ${identityMac}, 时间范围: ${startTimeStr} - ${endTimeStr}`)

    // 优先使用全量数据接口
    try {
      console.log('尝试使用全量数据接口获取自定义时间范围数据...')
      const fullResponse = await adminApi.get(`/api/v1/admin/terminal/metrics/device/${identityMac}/range/all`, {
        params: {
          startTime: startTimeStr,
          endTime: endTimeStr
        }
      })

      console.log(`自定义时间范围全量数据API响应: 总数据量=${fullResponse.totalElements}, 获取数据量=${fullResponse.content?.length}`)
      return fullResponse
    } catch (error) {
      console.warn('全量数据接口调用失败，尝试使用分页接口:', error.message)

      // 备用方案：使用分页接口获取大量数据
      return adminApi.get(`/api/v1/admin/terminal/metrics/device/${identityMac}/range`, {
        params: {
          startTime: startTimeStr,
          endTime: endTimeStr,
          page: 0,
          size: 10000 // 获取大量数据
        }
      })
    }
  }
}

// 告警相关API
export const alertApi = {
  // 获取告警列表
  async getAlerts(params = {}) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      let filteredAlerts = [...mockAlerts]

      // 模拟过滤逻辑
      if (params.alertId) {
        filteredAlerts = filteredAlerts.filter(a =>
          a.alertId && a.alertId.toLowerCase().includes(params.alertId.toLowerCase())
        )
      }
      if (params.identityMac) {
        filteredAlerts = filteredAlerts.filter(a =>
          a.identityMac.toLowerCase().includes(params.identityMac.toLowerCase())
        )
      }
      if (params.alertType) {
        filteredAlerts = filteredAlerts.filter(a => a.alertType === params.alertType)
      }
      if (params.alertStatus) {
        filteredAlerts = filteredAlerts.filter(a => a.alertStatus === params.alertStatus)
      }

      return {
        code: 200,
        data: {
          list: filteredAlerts,
          total: filteredAlerts.length,
          page: params.page || 1,
          pageSize: params.pageSize || 10
        },
        message: 'success'
      }
    }

    // 真实API调用 - 使用管理服务
    // 将identityMac参数传递给后端
    const apiParams = { ...params }
    if (params.identityMac) {
      apiParams.identityMac = params.identityMac
      delete apiParams.deviceId // 删除旧的deviceId参数
    }
    return adminApi.get('/api/v1/admin/alerts', { params: apiParams })
  },

  // 获取告警详情
  async getAlertDetail(alertId) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      const alert = mockAlerts.find(a => a.alertId === alertId)
      if (!alert) {
        throw new Error('告警不存在')
      }
      return {
        code: 200,
        data: alert,
        message: 'success'
      }
    }

    // 真实API调用 - 使用管理服务
    return adminApi.get(`/api/v1/admin/alerts/${alertId}`)
  },

  // 确认告警
  async acknowledgeAlert(alertId, acknowledgedBy) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      const index = mockAlerts.findIndex(a => a.alertId === alertId)
      if (index === -1) {
        throw new Error('告警不存在')
      }

      mockAlerts[index].alertStatus = 'ACKNOWLEDGED'
      mockAlerts[index].acknowledgedBy = acknowledgedBy
      mockAlerts[index].acknowledgedTime = new Date().toISOString()

      return {
        code: 200,
        data: mockAlerts[index],
        message: '告警已确认'
      }
    }

    // 真实API调用 - 使用管理服务
    return adminApi.post(`/api/v1/admin/alerts/${alertId}/acknowledge`, {
      acknowledgedBy
    })
  },

  // 解决告警
  async resolveAlert(alertId, resolvedBy, resolveComment) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      const index = mockAlerts.findIndex(a => a.alertId === alertId)
      if (index === -1) {
        throw new Error('告警不存在')
      }

      mockAlerts[index].alertStatus = 'RESOLVED'
      mockAlerts[index].resolvedBy = resolvedBy
      mockAlerts[index].resolvedTime = new Date().toISOString()
      mockAlerts[index].resolveComment = resolveComment

      return {
        code: 200,
        data: mockAlerts[index],
        message: '告警已解决'
      }
    }

    // 真实API调用 - 使用管理服务
    return adminApi.post(`/api/v1/admin/alerts/${alertId}/resolve`, {
      resolvedBy,
      resolveComment
    })
  },

  // 发送钉钉通知
  async sendDingTalkNotification(alertId) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      const index = mockAlerts.findIndex(a => a.alertId === alertId)
      if (index === -1) {
        throw new Error('告警不存在')
      }

      mockAlerts[index].notificationSent = true

      return {
        code: 200,
        message: '钉钉通知已发送'
      }
    }

    // 真实API调用 - 使用管理服务
    return adminApi.post(`/api/v1/admin/alerts/${alertId}/notify`)
  },

  // 获取告警统计信息
  async getAlertStatistics() {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      return {
        code: 200,
        data: {
          activeAlerts: 5,
          resolvedAlerts: 10,
          acknowledgedAlerts: 3,
          totalAlerts: 18,
          alertsByType: {
            CPU_TEMPERATURE: 2,
            MEMORY_USAGE: 1,
            DISK_USAGE: 2,
            LICENSE_EXPIRY: 0
          }
        },
        message: 'success'
      }
    }

    // 真实API调用 - 使用管理服务
    return adminApi.get('/api/v1/admin/alerts/statistics')
  }
}

// 统计数据API - 使用管理服务 (backend_admin_server:8080)
export const statsApi = {
  // 获取概览统计
  async getOverviewStats() {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      return {
        code: 200,
        data: mockStats,
        message: 'success'
      }
    }

    // 真实API调用 - 使用管理服务
    return adminApi.get('/api/v1/admin/overview/stats')
  },

  // 获取概览初始化数据
  async getOverviewInitData(terminalLimit = 10, alertLimit = 10) {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      return {
        code: 200,
        data: {
          stats: mockStats,
          offlineTerminals: [],
          alertTerminals: [],
          timestamp: new Date().toISOString()
        },
        message: 'success'
      }
    }

    // 真实API调用 - 使用管理服务
    return adminApi.get(`/api/v1/admin/overview/init?terminalLimit=${terminalLimit}&alertLimit=${alertLimit}`)
  },

  // 获取离线终端列表
  async getOfflineTerminals(limit = 10) {
    return adminApi.get(`/api/v1/admin/overview/offline-terminals?limit=${limit}`)
  },

  // 获取告警终端列表
  async getAlertTerminals(limit = 10) {
    return adminApi.get(`/api/v1/admin/overview/alert-terminals?limit=${limit}`)
  },

  // 获取实时动态
  async getRealTimeLogs() {
    // 检查是否使用Mock数据
    if (import.meta.env.VITE_USE_MOCK === 'true') {
      await delay()
      const logs = [
        { time: '14:32:10', event: 'TERM-003 设备上线', type: 'online' },
        { time: '14:30:25', event: 'TERM-001 CPU告警', type: 'alert' },
        { time: '14:28:15', event: 'TERM-002 设备离线', type: 'offline' },
        { time: '14:25:30', event: 'TERM-001 内存告警解除', type: 'resolved' },
        { time: '14:20:15', event: 'TERM-004 设备上线', type: 'online' }
      ]
      return {
        code: 200,
        data: logs,
        message: 'success'
      }
    }

    // 真实API调用 - 使用数据服务
    return dataApi.get('/api/overview/logs')
  }
}

// 认证相关API - 使用管理服务 (backend_admin_server:8080)
export const authApi = {
  // 获取验证码
  getCaptcha: () => adminApi.get('/api/v1/auth/captcha'),

  // 获取公钥
  getPublicKey: () => adminApi.get('/api/v1/auth/publicKey'),

  // 用户登录
  login: (credentials) => adminApi.post('/api/v1/auth/login?method=password', credentials),

  // 用户登出
  logout: () => adminApi.get('/api/v1/auth/logout'),

  // 刷新Token
  refreshToken: () => adminApi.get('/api/v1/auth/refreshToken'),

  // 获取用户信息
  getUserInfo: () => adminApi.get('/api/v1/account/me')
}

// 用户管理相关API - 使用管理服务 (backend_admin_server:8080)
export const userManagementApi = {
  // 获取用户列表
  getUserList: (params) => adminApi.get('/api/v1/admin/user-management/users', { params }),

  // 获取用户详情
  getUserDetail: (userId) => adminApi.get(`/api/v1/admin/users/${userId}`),

  // 创建用户
  createUser: (userData) => adminApi.post('/api/v1/admin/users', userData),

  // 更新用户状态
  updateUserStatus: (userId, enable) => adminApi.put(`/api/v1/admin/users/${userId}`, { enable }),

  // 更新用户信息
  updateUserInfo: (userId, userInfo) => adminApi.put(`/api/v1/admin/users/${userId}`, userInfo),

  // 获取用户角色
  getUserRoles: (userId) => adminApi.get(`/api/v1/admin/user-management/users/${userId}/roles`),

  // 分配用户角色
  assignUserRole: (userId, roleName) => adminApi.post(`/api/v1/admin/user-management/users/${userId}/roles`, null, { params: { roleName } }),

  // 移除用户角色
  removeUserRole: (userId, roleName) => adminApi.delete(`/api/v1/admin/user-management/users/${userId}/roles`, { params: { roleName } }),

  // 获取可用角色
  getAvailableRoles: () => adminApi.get('/api/v1/admin/user-management/roles')
}

// 指标配置管理相关API - 使用管理服务 (backend_admin_server:8080)
export const metricConfigApi = {
  // 获取指标配置列表
  getConfigList: (params) => adminApi.get('/api/v1/admin/metric-threshold-config/page', { params }),

  // 获取指标配置详情
  getConfigDetail: (id) => adminApi.get(`/api/v1/admin/metric-threshold-config/${id}`),

  // 创建指标配置
  createConfig: (configData) => adminApi.post('/api/v1/admin/metric-threshold-config', configData),

  // 更新指标配置
  updateConfig: (id, configData) => adminApi.put(`/api/v1/admin/metric-threshold-config/${id}`, configData),

  // 删除指标配置
  deleteConfig: (id) => adminApi.delete(`/api/v1/admin/metric-threshold-config/${id}`),

  // 批量删除指标配置
  batchDeleteConfig: (ids) => adminApi.delete('/api/v1/admin/metric-threshold-config/batch', { data: ids }),

  // 启用/禁用指标配置
  toggleConfig: (id, enabled) => adminApi.put(`/api/v1/admin/metric-threshold-config/${id}/toggle`, null, { params: { enabled } }),

  // 根据指标类型获取配置
  getConfigByType: (metricType) => adminApi.get(`/api/v1/admin/metric-threshold-config/metric-type/${metricType}`),

  // 获取所有启用的配置
  getAllEnabledConfigs: () => adminApi.get('/api/v1/admin/metric-threshold-config/enabled'),

  // 获取指标类型选项
  getMetricTypeOptions: () => adminApi.get('/api/v1/admin/metric-threshold-config/options/metric-types'),

  // 获取告警级别选项
  getAlertLevelOptions: () => adminApi.get('/api/v1/admin/metric-threshold-config/options/alert-levels'),

  // 从JSON数据中提取字段路径
  extractFieldPaths: (jsonData) => adminApi.post('/api/v1/admin/metric-threshold-config/extract-field-paths', jsonData, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 缓存管理相关API - 使用数据服务 (backend_server:8081)
export const cacheApi = {
  // 刷新指标阈值配置缓存
  refreshThresholdConfigCache: () => {
    // 为缓存刷新接口添加API Key，注意backend_server有context-path: /api
    return dataApi.post('/api/cache/refresh/threshold-config', {}, {
      headers: {
        'X-API-Key': import.meta.env.VITE_API_KEY || 'api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e'
      }
    })
  },

  // 获取缓存统计信息
  getCacheStats: () => {
    return dataApi.get('/api/cache/stats', {
      headers: {
        'X-API-Key': import.meta.env.VITE_API_KEY || 'api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e'
      }
    })
  },

  // 清空指定缓存
  evictCache: (cacheName) => {
    return dataApi.post(`/api/cache/evict/${cacheName}`, {}, {
      headers: {
        'X-API-Key': import.meta.env.VITE_API_KEY || 'api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e'
      }
    })
  },

  // 清空所有缓存
  evictAllCaches: () => {
    return dataApi.post('/api/cache/evict-all', {}, {
      headers: {
        'X-API-Key': import.meta.env.VITE_API_KEY || 'api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e'
      }
    })
  }
}

// 日志文件管理API - 使用管理服务 (backend_admin_server:8080)
export const logFileApi = {
  // 获取终端日志文件列表
  async getLogFilesByTerminal(identityMac, params = {}) {
    const { page = 1, size = 10 } = params
    return adminApi.get(`/api/v1/admin/log-files/terminal/${identityMac}`, {
      params: { page, size }
    })
  },

  // 获取终端所有日志文件
  async getAllLogFilesByTerminal(identityMac) {
    return adminApi.get(`/api/v1/admin/log-files/terminal/${identityMac}/all`)
  },

  // 获取文件下载URL
  async getDownloadUrl(fileId) {
    return adminApi.get(`/api/v1/admin/log-files/${fileId}/download-url`)
  },

  // 直接下载文件
  async downloadFile(fileId) {
    return adminApi.get(`/api/v1/admin/log-files/${fileId}/download`, {
      responseType: 'blob',
      timeout: 300000, // 5分钟超时
      onDownloadProgress: (progressEvent) => {
        // 可以在这里处理下载进度
        if (progressEvent.lengthComputable) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          console.log(`下载进度: ${percentCompleted}%`)
        }
      }
    })
  },

  // 删除日志文件
  async deleteLogFile(fileId) {
    return adminApi.delete(`/api/v1/admin/log-files/${fileId}`)
  },

  // 获取文件详情
  async getLogFileById(fileId) {
    return adminApi.get(`/api/v1/admin/log-files/${fileId}`)
  }
}