package com.unnet.changan5G.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.unnet.changan5G.dto.terminal.TerminalBasicInfo;
import com.unnet.changan5G.entity.TerminalBasicInfoEntity;
import com.unnet.changan5G.service.TerminalRegistrationService;
import com.unnet.changan5G.service.DeviceNotificationService;
import com.unnet.changan5G.service.TerminalBasicInfoService;
import com.unnet.changan5G.service.TerminalCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 终端状态检查定时任务（简化版）
 *
 * 功能：
 * 1. 定期发送统计信息更新（主要功能）
 * 2. 作为Redis键过期机制的兜底保障（备用功能）
 * 
 * 注意：主要的离线检查逻辑已移至RedisKeyExpirationListener中通过Redis TTL自动处理
 *
 * <AUTHOR>
 * @date 2024-07-14
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TerminalStatusCheckTask {

    private final TerminalRegistrationService terminalRegistrationService;
    private final DeviceNotificationService deviceNotificationService;
    private final TerminalBasicInfoService terminalBasicInfoService;
    private final TerminalCacheService terminalCacheService;

    /**
     * 设备状态统计定时任务
     * 每30秒执行一次，输出设备状态统计信息
     * 
     * 注意：离线检查逻辑已移至Redis键过期事件监听器中自动处理
     */
    @Scheduled(fixedRate = 30000) // 30秒
    public void logDeviceStatistics() {
        try {
            long onlineCount = terminalRegistrationService.getOnlineDeviceCount();
            long offlineCount = terminalRegistrationService.getOfflineDeviceCount();
            long totalCount = onlineCount + offlineCount;

            if (totalCount > 0) {
                log.info("设备状态统计 - 总数: {}, 在线: {} ({}%), 离线: {} ({}%)",
                        totalCount,
                        onlineCount, String.format("%.1f", (onlineCount * 100.0 / totalCount)),
                        offlineCount, String.format("%.1f", (offlineCount * 100.0 / totalCount)));
            } else {
                log.info("当前无设备注册");
            }

        } catch (Exception e) {
            log.error("输出设备统计信息时发生错误: {}", e.getMessage(), e);
        }
    }

//    /**
//     * Redis键过期机制兜底检查（可选）
//     * 每5分钟执行一次，作为Redis键过期机制的备用保障
//     *
//     * 注意：这是一个轻量级的兜底机制，正常情况下Redis键过期事件会自动处理离线逻辑
//     */
//    @Scheduled(fixedRate = 300000) // 5分钟
//    public void fallbackOfflineCheck() {
//        try {
//            log.debug("执行Redis键过期机制兜底检查");
//
//            // 这里可以实现一个轻量级的检查逻辑
//            // 比如检查数据库中状态为在线但长时间未更新的设备
//            // 由于主要逻辑已由Redis TTL处理，这里只做必要的兜底检查
//
//            // TODO: 如果需要兜底机制，可以在这里实现
//            // 例如：检查数据库中status=1但last_update_time超过5分钟的设备
//
//        } catch (Exception e) {
//            log.error("执行兜底离线检查时发生错误: {}", e.getMessage(), e);
//        }
//    }

    /**
     * 定期发送统计信息更新通知
     * 每分钟执行一次，确保前端统计信息保持最新
     */
    @Scheduled(fixedRate = 60000) // 60秒
    public void sendPeriodicStatisticsUpdate() {
        try {
            sendStatisticsUpdateNotification();
            log.debug("定期统计信息更新通知已发送");
        } catch (Exception e) {
            log.error("发送定期统计信息更新通知失败: {}", e.getMessage(), e);
        }
    }



    /**
     * 发送统计信息更新通知
     */
    private void sendStatisticsUpdateNotification() {
        try {
            // 获取最新的统计信息
            int onlineCount = terminalBasicInfoService.countOnlineDevices();
            int offlineCount = terminalBasicInfoService.countOfflineDevices();
            int alertCount = 0; // 这里可以根据需要获取告警数量

            // 发送统计信息更新通知
            deviceNotificationService.sendStatisticsUpdateNotification(
                    onlineCount, offlineCount, alertCount);

            log.debug("发送统计信息更新通知 - 在线: {}, 离线: {}, 告警: {}",
                    onlineCount, offlineCount, alertCount);

        } catch (Exception e) {
            log.error("发送统计信息更新通知失败: {}", e.getMessage(), e);
        }
    }
}
