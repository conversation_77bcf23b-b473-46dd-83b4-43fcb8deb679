package com.unnet.changan5G.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.unnet.changan5G.dto.terminal.TerminalBasicInfo;
import com.unnet.changan5G.entity.TerminalBasicInfoEntity;
import com.unnet.changan5G.mapper.TerminalBasicInfoMapper;
import com.unnet.changan5G.service.TerminalBasicInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 终端基本信息服务实现
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TerminalBasicInfoServiceImpl extends ServiceImpl<TerminalBasicInfoMapper, TerminalBasicInfoEntity> 
        implements TerminalBasicInfoService {

    private final TerminalBasicInfoMapper terminalBasicInfoMapper;

    @Override
    public TerminalBasicInfoEntity getByDeviceId(String deviceId) {
        return terminalBasicInfoMapper.selectByIdentityMac(deviceId);
    }

    @Override
    public TerminalBasicInfoEntity getByIdentityMac(String identityMac) {
        return terminalBasicInfoMapper.selectByIdentityMac(identityMac);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateTerminalInfo(TerminalBasicInfo terminalBasicInfo) {
        try {
            TerminalBasicInfoEntity entity = new TerminalBasicInfoEntity();
            BeanUtils.copyProperties(terminalBasicInfo, entity);
            
            // 检查设备是否已存在
            TerminalBasicInfoEntity existingEntity = getByIdentityMac(terminalBasicInfo.getIdentityMac());
            
            if (existingEntity != null) {
                // 更新现有设备信息
                log.info("更新现有设备信息：{}",entity.getIdentityMac());
                entity.setId(existingEntity.getId());
                entity.setFirstRegisterTime(existingEntity.getFirstRegisterTime()); // 保持首次注册时间不变
                return updateById(entity);
            } else {
                // 新设备注册
                log.info("保存新的信息：{}",entity.getIdentityMac());
                entity.setFirstRegisterTime(LocalDateTime.now());
                return save(entity);
            }
        } catch (Exception e) {
            log.error("保存或更新终端基本信息失败 - 设备: {}, 错误: {}", 
                    terminalBasicInfo.getDeviceId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDeviceStatus(String deviceId, Integer status) {
        try {
            int result = terminalBasicInfoMapper.updateStatusByDeviceId(deviceId, status, LocalDateTime.now());
            log.debug("更新设备状态 - 设备: {}, 状态: {}, 影响行数: {}", deviceId, status, result);
            return result > 0;
        } catch (Exception e) {
            log.error("更新设备状态失败 - 设备: {}, 状态: {}, 错误: {}", deviceId, status, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateDeviceStatus(List<String> deviceIds, Integer status) {
        try {
            if (deviceIds == null || deviceIds.isEmpty()) {
                return true;
            }
            int result = terminalBasicInfoMapper.batchUpdateStatusByDeviceIds(deviceIds, status, LocalDateTime.now());
            log.debug("批量更新设备状态 - 设备数量: {}, 状态: {}, 影响行数: {}", deviceIds.size(), status, result);
            return result > 0;
        } catch (Exception e) {
            log.error("批量更新设备状态失败 - 设备数量: {}, 状态: {}, 错误: {}", 
                    deviceIds.size(), status, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean existsByDeviceId(String deviceId) {
        return getByDeviceId(deviceId) != null;
    }

    @Override
    public boolean existsByIdentityMac(String identityMac) {
        return terminalBasicInfoMapper.selectByIdentityMac(identityMac) != null;
    }

    @Override
    public long getOnlineDeviceCount() {
        return terminalBasicInfoMapper.countOnlineDevices();
    }

    @Override
    public long getOfflineDeviceCount() {
        return terminalBasicInfoMapper.countOfflineDevices();
    }

    @Override
    public List<TerminalBasicInfoEntity> getDeviceLastUpdateTimes() {
        return terminalBasicInfoMapper.selectDeviceLastUpdateTimes();
    }

    @Override
    public List<TerminalBasicInfoEntity> getDevicesByTag(String tagKey, String tagValue) {
        return terminalBasicInfoMapper.selectByTag(tagKey, tagValue);
    }

    @Override
    public List<TerminalBasicInfoEntity> getExpiringDevices(int days) {
        return terminalBasicInfoMapper.selectExpiringDevices(days);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean registerNewDevice(TerminalBasicInfo terminalBasicInfo) {
        try {
            // 检查设备是否已存在
            if (existsByIdentityMac(terminalBasicInfo.getIdentityMac())) {
                log.warn("设备已存在，无需重复注册 - 设备: {}", terminalBasicInfo.getIdentityMac());
                return false;
            }

            TerminalBasicInfoEntity entity = new TerminalBasicInfoEntity();
            BeanUtils.copyProperties(terminalBasicInfo, entity);
            entity.setFirstRegisterTime(LocalDateTime.now());
            entity.setStatus(1); // 设置为在线状态
            entity.setDataSource("kafka");

            boolean result = save(entity);
            if (result) {
                log.info("新设备注册成功 - 设备: {}, 主机名: {}", 
                        terminalBasicInfo.getDeviceId(), terminalBasicInfo.getHostname());
            }
            return result;
        } catch (Exception e) {
            log.error("新设备注册失败 - 设备: {}, 错误: {}", 
                    terminalBasicInfo.getDeviceId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLastReportTime(String deviceId, LocalDateTime reportTime) {
        try {
            int result = terminalBasicInfoMapper.updateStatusByDeviceId(deviceId, 1, reportTime);
            log.debug("更新设备最后上报时间 - 设备: {}, 时间: {}", deviceId, reportTime);
            return result > 0;
        } catch (Exception e) {
            log.error("更新设备最后上报时间失败 - 设备: {}, 错误: {}", deviceId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLastReportTimeByMac(String identityMac, LocalDateTime reportTime) {
        try {
            int result = terminalBasicInfoMapper.updateStatusByIdentityMac(identityMac, 1, reportTime);
            log.debug("更新设备最后上报时间 - 设备MAC: {}, 时间: {}", identityMac, reportTime);
            return result > 0;
        } catch (Exception e) {
            log.error("更新设备最后上报时间失败 - 设备MAC: {}, 错误: {}", identityMac, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int countOnlineDevices() {
        try {
            int count = terminalBasicInfoMapper.countByStatus(1);
            log.debug("统计在线设备数量: {}", count);
            return count;
        } catch (Exception e) {
            log.error("统计在线设备数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int countOfflineDevices() {
        try {
            int count = terminalBasicInfoMapper.countByStatus(0);
            log.debug("统计离线设备数量: {}", count);
            return count;
        } catch (Exception e) {
            log.error("统计离线设备数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDeviceStatusByMac(String mac, int status) {
        try {
            if (mac == null || mac.trim().isEmpty()) {
                log.warn("MAC地址为空，无法更新设备状态");
                return false;
            }

            int updateCount = terminalBasicInfoMapper.updateStatusByIdentityMac(mac, status, LocalDateTime.now());
            boolean success = updateCount > 0;
            log.debug("根据MAC地址更新设备状态 - MAC: {}, 状态: {}, 结果: {}", mac, status, success);
            return success;
        } catch (Exception e) {
            log.error("根据MAC地址更新设备状态失败 - MAC: {}, 状态: {}, 错误: {}", 
                      mac, status, e.getMessage(), e);
            return false;
        }
    }
}
