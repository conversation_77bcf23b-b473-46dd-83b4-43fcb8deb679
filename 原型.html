<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统实时监控仪表盘</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #2c3e50);
            color: #fff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        header {
            text-align: center;
            padding: 20px 0;
            margin-bottom: 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(90deg, #00c9ff, #92fe9d);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .subtitle {
            font-size: 1.1rem;
            color: #bbb;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .chart-title {
            font-size: 1.4rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            color: #e0e0e0;
        }
        
        .chart-title i {
            margin-right: 10px;
            font-size: 1.6rem;
            color: #00c9ff;
        }
        
        #main-chart {
            width: 100%;
            height: 500px;
        }
        
        .status-panel {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .panel-title {
            font-size: 1.4rem;
            margin-bottom: 25px;
            color: #e0e0e0;
            display: flex;
            align-items: center;
        }
        
        .panel-title i {
            margin-right: 10px;
            font-size: 1.6rem;
            color: #92fe9d;
        }
        
        .metric-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .metric-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 18px 15px;
            text-align: center;
            transition: transform 0.3s, background 0.3s;
            border: 1px solid rgba(255, 255, 255, 0.07);
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(0, 201, 255, 0.3);
        }
        
        .metric-name {
            font-size: 1rem;
            color: #aaa;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .metric-value {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-unit {
            font-size: 0.9rem;
            color: #888;
        }
        
        .metric-trend {
            font-size: 0.85rem;
            margin-top: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .trend-up {
            color: #ff6b6b;
        }
        
        .trend-down {
            color: #92fe9d;
        }
        
        .controls {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .control-group {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 18px;
            border: 1px solid rgba(255, 255, 255, 0.07);
        }
        
        .control-title {
            font-size: 1.1rem;
            margin-bottom: 15px;
            color: #e0e0e0;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 18px;
            border: none;
            border-radius: 6px;
            background: rgba(0, 201, 255, 0.15);
            color: #00c9ff;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
            flex: 1;
            min-width: 80px;
        }
        
        .btn:hover {
            background: rgba(0, 201, 255, 0.25);
        }
        
        .btn.active {
            background: rgba(0, 201, 255, 0.4);
            color: white;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #777;
            font-size: 0.9rem;
            margin-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        @media (max-width: 1100px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 600px) {
            .metric-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>系统实时监控仪表盘</h1>
            <p class="subtitle">监控上行实时速率、CPU温度、CPU使用率、内存使用率等关键指标，实时展示从今日开始到当前时间的变化趋势</p>
        </header>
        
        <div class="dashboard">
            <div class="chart-container">
                <div class="chart-title">
                    <i>📈</i> 多指标实时趋势图
                </div>
                <div id="main-chart"></div>
            </div>
            
            <div class="status-panel">
                <div class="panel-title">
                    <i>📊</i> 当前指标状态
                </div>
                
                <div class="metric-cards">
                    <div class="metric-card">
                        <div class="metric-name">上行实时速率</div>
                        <div class="metric-value">12.8</div>
                        <div class="metric-unit">Mbps</div>
                        <div class="metric-trend trend-up">↑ 2.1%</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-name">CPU温度</div>
                        <div class="metric-value">62.4</div>
                        <div class="metric-unit">°C</div>
                        <div class="metric-trend trend-down">↓ 1.3%</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-name">CPU使用率</div>
                        <div class="metric-value">45.2</div>
                        <div class="metric-unit">%</div>
                        <div class="metric-trend trend-up">↑ 3.7%</div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-name">内存使用率</div>
                        <div class="metric-value">78.3</div>
                        <div class="metric-unit">%</div>
                        <div class="metric-trend trend-down">↓ 0.8%</div>
                    </div>
                </div>
                
                <div class="controls">
                    <div class="control-group">
                        <div class="control-title">时间范围</div>
                        <div class="btn-group">
                            <button class="btn active">实时</button>
                            <button class="btn">1小时</button>
                            <button class="btn">6小时</button>
                            <button class="btn">24小时</button>
                        </div>
                    </div>
                    
                    <div class="control-group">
                        <div class="control-title">数据更新频率</div>
                        <div class="btn-group">
                            <button class="btn active">5秒</button>
                            <button class="btn">10秒</button>
                            <button class="btn">30秒</button>
                            <button class="btn">1分钟</button>
                        </div>
                    </div>
                    
                    <div class="control-group">
                        <div class="control-title">图表操作</div>
                        <div class="btn-group">
                            <button class="btn">暂停</button>
                            <button class="btn">重置缩放</button>
                            <button class="btn">导出图片</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            数据更新时间: <span id="update-time">--:--:--</span> | 系统状态: <span style="color:#92fe9d;">正常</span>
        </div>
    </div>

    <script>
        // 初始化图表
        const chartDom = document.getElementById('main-chart');
        const myChart = echarts.init(chartDom);
        
        // 生成模拟数据
        function generateData() {
            const now = new Date();
            const startOfDay = new Date();
            startOfDay.setHours(0, 0, 0, 0);
            
            const data = {
                time: [],
                uploadRate: [],
                cpuTemp: [],
                cpuUsage: [],
                memoryUsage: []
            };
            
            // 生成从今天开始到当前时间的数据点（每5分钟一个点）
            const interval = 5 * 60 * 1000; // 5分钟
            let currentTime = startOfDay.getTime();
            
            while (currentTime <= now.getTime()) {
                const time = new Date(currentTime);
                data.time.push(time);
                
                // 模拟数据（在实际应用中替换为真实数据）
                // 上行速率：白天较高，夜间较低
                const hour = time.getHours();
                const uploadBase = 10 + Math.sin(hour * Math.PI / 12) * 5;
                data.uploadRate.push(parseFloat((uploadBase + Math.random() * 3).toFixed(1)));
                
                // CPU温度：随负载变化
                const tempBase = 55 + Math.sin(hour * Math.PI / 12) * 5;
                data.cpuTemp.push(parseFloat((tempBase + Math.random() * 4).toFixed(1)));
                
                // CPU使用率：随机波动
                const cpuBase = 40 + Math.sin(hour * Math.PI / 6) * 10;
                data.cpuUsage.push(parseFloat((cpuBase + Math.random() * 15).toFixed(1)));
                
                // 内存使用率：逐渐上升趋势
                const memBase = 60 + (currentTime - startOfDay.getTime()) / (now.getTime() - startOfDay.getTime()) * 20;
                data.memoryUsage.push(parseFloat((memBase + Math.random() * 5).toFixed(1)));
                
                currentTime += interval;
            }
            
            return data;
        }
        
        // 图表配置
        const chartData = generateData();
        
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(40, 40, 60, 0.9)',
                borderColor: '#555',
                textStyle: {
                    color: '#fff'
                },
                axisPointer: {
                    type: 'cross',
                    label: {
                        backgroundColor: '#6a7985'
                    }
                }
            },
            legend: {
                data: ['上行速率', 'CPU温度', 'CPU使用率', '内存使用率'],
                textStyle: {
                    color: '#ccc'
                },
                right: 10,
                top: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'time',
                boundaryGap: false,
                axisLine: {
                    lineStyle: {
                        color: '#aaa'
                    }
                },
                axisLabel: {
                    color: '#999',
                    formatter: function(value) {
                        const date = new Date(value);
                        return `${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
                    }
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: 'rgba(255, 255, 255, 0.05)'
                    }
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '上行速率 (Mbps)',
                    nameTextStyle: {
                        color: '#00c9ff'
                    },
                    position: 'left',
                    axisLine: {
                        lineStyle: {
                            color: '#00c9ff'
                        }
                    },
                    axisLabel: {
                        formatter: '{value}',
                        color: '#00c9ff'
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 201, 255, 0.1)'
                        }
                    }
                },
                {
                    type: 'value',
                    name: '温度 (°C)',
                    nameTextStyle: {
                        color: '#ff7675'
                    },
                    position: 'right',
                    axisLine: {
                        lineStyle: {
                            color: '#ff7675'
                        }
                    },
                    axisLabel: {
                        formatter: '{value}',
                        color: '#ff7675'
                    },
                    splitLine: {
                        show: false
                    }
                },
                {
                    type: 'value',
                    name: '使用率 (%)',
                    nameTextStyle: {
                        color: '#92fe9d'
                    },
                    position: 'right',
                    offset: 60,
                    axisLine: {
                        lineStyle: {
                            color: '#92fe9d'
                        }
                    },
                    axisLabel: {
                        formatter: '{value}',
                        color: '#92fe9d'
                    },
                    splitLine: {
                        show: false
                    }
                }
            ],
            dataZoom: [
                {
                    type: 'inside',
                    start: 80,
                    end: 100
                },
                {
                    type: 'slider',
                    start: 80,
                    end: 100,
                    backgroundColor: 'rgba(255,255,255,0.05)',
                    borderColor: 'transparent',
                    fillerColor: 'rgba(0, 201, 255, 0.2)',
                    handleStyle: {
                        color: '#00c9ff'
                    },
                    textStyle: {
                        color: '#ccc'
                    }
                }
            ],
            series: [
                {
                    name: '上行速率',
                    type: 'line',
                    smooth: true,
                    yAxisIndex: 0,
                    symbol: 'circle',
                    symbolSize: 6,
                    lineStyle: {
                        width: 3
                    },
                    itemStyle: {
                        color: '#00c9ff'
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: 'rgba(0, 201, 255, 0.5)' },
                            { offset: 1, color: 'rgba(0, 201, 255, 0.1)' }
                        ])
                    },
                    data: chartData.time.map((time, index) => [time, chartData.uploadRate[index]])
                },
                {
                    name: 'CPU温度',
                    type: 'line',
                    smooth: true,
                    yAxisIndex: 1,
                    symbol: 'circle',
                    symbolSize: 6,
                    lineStyle: {
                        width: 3
                    },
                    itemStyle: {
                        color: '#ff7675'
                    },
                    data: chartData.time.map((time, index) => [time, chartData.cpuTemp[index]])
                },
                {
                    name: 'CPU使用率',
                    type: 'line',
                    smooth: true,
                    yAxisIndex: 2,
                    symbol: 'circle',
                    symbolSize: 6,
                    lineStyle: {
                        width: 3
                    },
                    itemStyle: {
                        color: '#92fe9d'
                    },
                    data: chartData.time.map((time, index) => [time, chartData.cpuUsage[index]])
                },
                {
                    name: '内存使用率',
                    type: 'line',
                    smooth: true,
                    yAxisIndex: 2,
                    symbol: 'circle',
                    symbolSize: 6,
                    lineStyle: {
                        width: 3,
                        type: 'dashed'
                    },
                    itemStyle: {
                        color: '#ffcc00'
                    },
                    data: chartData.time.map((time, index) => [time, chartData.memoryUsage[index]])
                }
            ]
        };
        
        // 设置图表选项
        myChart.setOption(option);
        
        // 响应窗口大小变化
        window.addEventListener('resize', function() {
            myChart.resize();
        });
        
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            document.getElementById('update-time').textContent = 
                `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
        }
        
        setInterval(updateTime, 1000);
        updateTime();
    </script>
</body>
</html>