//package com.unnet.changan5G.component;
//
//import com.unnet.changan5G.service.TerminalCacheService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.ApplicationArguments;
//import org.springframework.boot.ApplicationRunner;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
///**
// * Redis缓存初始化组件
// *
// * <AUTHOR>
// * @date 2024-07-15
// */
//@Component
//@Slf4j
//@RequiredArgsConstructor
//@Order(2) // 在ElasticsearchInitializer之后执行
//public class RedisCacheInitializer implements ApplicationRunner {
//
//    private final TerminalCacheService terminalCacheService;
//
//    @Override
//    public void run(ApplicationArguments args) throws Exception {
//        log.info("开始初始化Redis缓存...");
//
//        try {
//            // 预热缓存
//            terminalCacheService.warmUpCache();
//
//            log.info("Redis缓存初始化完成");
//
//        } catch (Exception e) {
//            log.error("Redis缓存初始化失败: {}", e.getMessage(), e);
//            // 不抛出异常，避免影响应用启动
//        }
//    }
//}
