//package com.unnet.changan5G.component;
//
//import com.unnet.changan5G.service.MetricThresholdConfigService;
//import com.unnet.changan5G.service.TerminalCacheService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.ApplicationArguments;
//import org.springframework.boot.ApplicationRunner;
//import org.springframework.cache.CacheManager;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
///**
// * Redis缓存初始化器 - 应用启动时预热缓存
// *
// * <AUTHOR>
// * @date 2024-07-24
// */
//@Component
//@Slf4j
//@RequiredArgsConstructor
//@Order(3) // 在其他初始化器之后执行
//public class CacheInitializer implements ApplicationRunner {
//
//    private final MetricThresholdConfigService metricThresholdConfigService;
//    private final TerminalCacheService terminalCacheService;
//    private final CacheManager cacheManager;
//
//    @Override
//    public void run(ApplicationArguments args) throws Exception {
//        try {
//            log.info("开始预热Redis缓存...");
//
//            // 检查Redis缓存管理器
//            log.info("当前缓存管理器类型: {}", cacheManager.getClass().getSimpleName());
//
//            // 预热指标阈值配置缓存
//            log.info("预热指标阈值配置缓存...");
//            metricThresholdConfigService.getAllEnabledConfigsMap();
//            log.info("指标阈值配置缓存预热完成");
//
//            // 预热终端缓存
//            log.info("预热终端基本信息缓存...");
//            terminalCacheService.warmUpCache();
//            log.info("终端基本信息缓存预热完成");
//
//            // 输出缓存统计信息
//            Object cacheStats = terminalCacheService.getCacheStatistics();
//            log.info("缓存统计信息: {}", cacheStats);
//
//            log.info("Redis缓存预热完成");
//
//        } catch (Exception e) {
//            log.error("Redis缓存预热失败: {}", e.getMessage(), e);
//            // 不抛出异常，避免影响应用启动
//        }
//    }
//}