# 实时指标页面UI改进测试清单

## 测试环境
- 浏览器：Chrome/Firefox/Safari/Edge
- 设备：桌面端/平板/手机
- 分辨率：1920x1080, 1366x768, 768x1024, 375x667

## 功能测试清单

### ✅ 时间范围选择测试

#### 1. 今日全天功能
- [ ] 点击"📊 今日全天"按钮
- [ ] 验证按钮激活状态（蓝色渐变背景）
- [ ] 确认图表显示今天0点到当前时间的数据
- [ ] 检查图表标题显示"今日全天"

#### 2. 自定义时间功能
- [ ] 点击"📅 自定义时间"按钮
- [ ] 验证弹框正常打开
- [ ] 测试时间选择器功能
- [ ] 验证快速预设按钮
- [ ] 确认应用后图表数据更新
- [ ] 检查图表标题显示自定义时间范围

### ✅ 图表缩放功能测试

#### 1. 滑动条缩放
- [ ] 确认图表底部始终显示滑动条
- [ ] 拖拽左侧手柄调整开始时间
- [ ] 拖拽右侧手柄调整结束时间
- [ ] 拖拽中间区域移动时间窗口
- [ ] 验证滑动条样式美观（蓝色渐变）

#### 2. 鼠标滚轮缩放
- [ ] 在图表区域向上滚动鼠标滚轮（放大）
- [ ] 在图表区域向下滚动鼠标滚轮（缩小）
- [ ] 验证缩放中心点正确
- [ ] 确认滑动条同步更新

#### 3. 拖拽移动
- [ ] 缩放后按住鼠标左键拖拽
- [ ] 验证可以查看不同时间段
- [ ] 确认拖拽流畅无卡顿

#### 4. 重置缩放
- [ ] 在缩放状态下点击"🔍 重置缩放"
- [ ] 验证图表恢复到完整视图
- [ ] 确认按钮状态正确更新（禁用状态）

### ✅ 界面样式测试

#### 1. 控制区域样式
- [ ] 验证控制区域有渐变背景
- [ ] 确认圆角边框显示正常
- [ ] 检查内边距和外边距合适
- [ ] 验证阴影效果

#### 2. 按钮样式测试
- [ ] **今日全天按钮**：蓝色渐变，带图标
- [ ] **自定义时间按钮**：蓝色渐变，带日历图标
- [ ] **重置缩放按钮**：绿色渐变，带放大镜图标
- [ ] **指标配置按钮**：橙色渐变，带设置图标

#### 3. 悬停效果测试
- [ ] 鼠标悬停按钮时有轻微上移动画
- [ ] 悬停时阴影加深
- [ ] 颜色变化自然流畅
- [ ] 离开时恢复原状

#### 4. 激活状态测试
- [ ] 激活的时间范围按钮有明显视觉区分
- [ ] 非激活按钮样式正常
- [ ] 状态切换动画流畅

### ✅ 响应式设计测试

#### 1. 桌面端 (> 1024px)
- [ ] 控制区域水平布局
- [ ] 按钮大小适中
- [ ] 间距合理
- [ ] 所有功能正常

#### 2. 平板端 (768px - 1024px)
- [ ] 布局保持水平但间距调整
- [ ] 按钮尺寸适合触摸
- [ ] 滑动条操作方便
- [ ] 文字大小合适

#### 3. 移动端 (< 768px)
- [ ] 控制区域垂直布局
- [ ] 时间选择按钮居中显示
- [ ] 控制按钮居中排列
- [ ] 按钮最小宽度满足触摸需求
- [ ] 滑动条在小屏幕上可操作

### ✅ 用户体验测试

#### 1. 提示信息
- [ ] 图表下方显示操作提示
- [ ] 提示文字清晰易懂
- [ ] 样式不干扰主要内容
- [ ] 在有数据时才显示

#### 2. 数据显示
- [ ] 数据点数量正确显示
- [ ] 时间范围信息准确
- [ ] 自定义时间范围标签完整
- [ ] 图表标题信息丰富

#### 3. 交互反馈
- [ ] 按钮点击有即时反馈
- [ ] 加载状态有适当提示
- [ ] 错误信息清晰明确
- [ ] 成功操作有确认提示

## 性能测试

### 1. 渲染性能
- [ ] 页面初始加载速度正常
- [ ] 按钮动画流畅不卡顿
- [ ] 滑动条操作响应及时
- [ ] 大数据量下性能稳定

### 2. 内存使用
- [ ] 长时间使用无内存泄漏
- [ ] 切换时间范围内存释放正常
- [ ] 缩放操作不占用过多内存

## 兼容性测试

### 1. 浏览器兼容性
- [ ] Chrome 80+ 显示正常
- [ ] Firefox 75+ 功能完整
- [ ] Safari 13+ 样式正确
- [ ] Edge 80+ 交互正常

### 2. 设备兼容性
- [ ] Windows 桌面端
- [ ] macOS 桌面端
- [ ] iOS 移动端
- [ ] Android 移动端
- [ ] iPad 平板端

## 边界情况测试

### 1. 数据边界
- [ ] 无数据时的显示
- [ ] 单个数据点的处理
- [ ] 大量数据点的性能
- [ ] 时间跨度很大的情况

### 2. 操作边界
- [ ] 快速连续点击按钮
- [ ] 极限缩放操作
- [ ] 网络断开时的处理
- [ ] 页面刷新后状态保持

## 问题记录模板

### 发现问题时请记录：
```
问题标题：[简短描述]
重现步骤：
1. 
2. 
3. 

预期结果：
实际结果：
设备信息：
浏览器版本：
截图/录屏：[如适用]
优先级：高/中/低
```

## 测试完成标准

### 必须通过的测试项：
- ✅ 所有基础功能正常工作
- ✅ 响应式设计在主要设备上正确显示
- ✅ 主流浏览器兼容性良好
- ✅ 性能表现满足要求
- ✅ 用户体验流畅自然

### 可接受的已知问题：
- 极旧版本浏览器的样式差异
- 特殊分辨率下的细微布局调整
- 极端数据量下的性能影响

---

**测试负责人：** ___________  
**测试日期：** ___________  
**测试结果：** 通过 / 需要修复  
**备注：** ___________
