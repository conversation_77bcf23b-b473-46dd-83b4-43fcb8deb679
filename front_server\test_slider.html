<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滑动条样式测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a73e8;
            margin-bottom: 20px;
        }
        #chart {
            width: 100%;
            height: 400px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }
        .chart-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }
        .section-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .chart-small {
            width: 100%;
            height: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="chart-title">滑动条样式对比测试</div>
        <div id="chart"></div>
        
        <div class="comparison">
            <div class="chart-section">
                <div class="section-title">原型样式（参考）</div>
                <div id="chart-prototype" class="chart-small"></div>
            </div>
            <div class="chart-section">
                <div class="section-title">优化后样式</div>
                <div id="chart-optimized" class="chart-small"></div>
            </div>
        </div>
    </div>

    <script>
        // 生成测试数据
        function generateTestData() {
            const data = [];
            const now = new Date();
            for (let i = 0; i < 100; i++) {
                const time = new Date(now.getTime() - (100 - i) * 60000);
                data.push([
                    time,
                    Math.sin(i * 0.1) * 50 + 50 + Math.random() * 10
                ]);
            }
            return data;
        }

        const testData = generateTestData();

        // 主图表 - 使用优化后的样式
        const mainChart = echarts.init(document.getElementById('chart'));
        const mainOption = {
            title: {
                text: '实时指标趋势（优化后滑动条）',
                textStyle: { color: '#333', fontSize: 16 }
            },
            tooltip: { trigger: 'axis' },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'time',
                boundaryGap: false
            },
            yAxis: {
                type: 'value'
            },
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                },
                {
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    // 测试拖拽功能的配置
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                    borderColor: 'transparent',
                    fillerColor: 'rgba(26, 115, 232, 0.2)',
                    handleStyle: {
                        color: '#1a73e8',
                        borderColor: '#ffffff',
                        borderWidth: 2,
                        shadowBlur: 3,
                        shadowColor: 'rgba(26, 115, 232, 0.3)'
                    },
                    moveHandleStyle: {
                        color: 'rgba(26, 115, 232, 0.1)',
                        borderColor: 'rgba(26, 115, 232, 0.3)',
                        borderWidth: 1
                    },
                    emphasis: {
                        handleStyle: {
                            color: '#0d47a1',
                            borderWidth: 3,
                            shadowBlur: 5,
                            shadowColor: 'rgba(13, 71, 161, 0.5)'
                        },
                        moveHandleStyle: {
                            color: 'rgba(26, 115, 232, 0.2)',
                            borderColor: 'rgba(26, 115, 232, 0.5)'
                        }
                    },
                    textStyle: {
                        color: '#5f6368',
                        fontSize: 11
                    },
                    // 启用拖拽功能的关键配置
                    handleIcon: 'circle',
                    handleSize: '120%',
                    moveOnMouseMove: true,
                    moveOnMouseWheel: false,
                    brushSelect: false,
                    throttle: 100,
                    rangeMode: ['percent', 'percent'],
                    height: 25,
                    bottom: 8,
                    left: '3%',
                    right: '3%',
                    realtime: true,
                    zoomLock: false
                }
            ],
            series: [{
                name: '测试指标',
                type: 'line',
                smooth: true,
                data: testData,
                lineStyle: { width: 2, color: '#1a73e8' },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(26, 115, 232, 0.3)' },
                        { offset: 1, color: 'rgba(26, 115, 232, 0.05)' }
                    ])
                }
            }]
        };
        mainChart.setOption(mainOption);

        // 原型样式图表
        const prototypeChart = echarts.init(document.getElementById('chart-prototype'));
        const prototypeOption = {
            title: {
                text: '原型样式',
                textStyle: { color: '#333', fontSize: 14 }
            },
            tooltip: { trigger: 'axis' },
            grid: { left: '3%', right: '4%', bottom: '15%', top: '15%', containLabel: true },
            xAxis: { type: 'time', boundaryGap: false },
            yAxis: { type: 'value' },
            dataZoom: [
                { type: 'inside', start: 80, end: 100 },
                {
                    type: 'slider',
                    start: 80,
                    end: 100,
                    backgroundColor: 'rgba(255,255,255,0.05)',
                    borderColor: 'transparent',
                    fillerColor: 'rgba(0, 201, 255, 0.2)',
                    handleStyle: { color: '#00c9ff' },
                    textStyle: { color: '#ccc' }
                }
            ],
            series: [{
                name: '原型指标',
                type: 'line',
                smooth: true,
                data: testData,
                lineStyle: { width: 2, color: '#00c9ff' },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(0, 201, 255, 0.3)' },
                        { offset: 1, color: 'rgba(0, 201, 255, 0.05)' }
                    ])
                }
            }]
        };
        prototypeChart.setOption(prototypeOption);

        // 优化后样式图表
        const optimizedChart = echarts.init(document.getElementById('chart-optimized'));
        optimizedChart.setOption(mainOption);

        // 响应式
        window.addEventListener('resize', () => {
            mainChart.resize();
            prototypeChart.resize();
            optimizedChart.resize();
        });
    </script>
</body>
</html>
