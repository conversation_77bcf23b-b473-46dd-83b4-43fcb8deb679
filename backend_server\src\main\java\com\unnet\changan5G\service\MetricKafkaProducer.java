package com.unnet.changan5G.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

/**
 * 指标数据Kafka生产者服务
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MetricKafkaProducer {

    private static final String TOPIC_NAME = "5g_metric";
    
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;

    /**
     * 发送原始JSON字符串到Kafka
     * 
     * @param jsonData JSON字符串
     * @param identityMac 设备ID作为消息key
     */
    public void sendRawJsonData(String jsonData, String identityMac) {
        try {
            log.info("准备发送原始JSON数据到Kafka - Topic: {}, identityMac: {}", TOPIC_NAME, identityMac);
            log.debug("发送的原始JSON数据: {}", jsonData);
            
            ListenableFuture<SendResult<String, String>> future = 
                kafkaTemplate.send(TOPIC_NAME, identityMac, jsonData);
            
            future.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
                @Override
                public void onSuccess(SendResult<String, String> result) {
                    log.info("原始JSON数据发送成功 - Topic: {}, Partition: {}, Offset: {}, identityMac: {}",
                            result.getRecordMetadata().topic(),
                            result.getRecordMetadata().partition(),
                            result.getRecordMetadata().offset(),
                            identityMac);
                }

                @Override
                public void onFailure(Throwable ex) {
                    log.error("原始JSON数据发送失败 - identityMac: {}, 错误: {}", identityMac, ex.getMessage(), ex);
                }
            });
            
        } catch (Exception e) {
            log.error("发送原始JSON数据到Kafka失败 - identityMac: {}, 错误: {}", identityMac, e.getMessage(), e);
            throw new RuntimeException("发送原始JSON数据到Kafka失败", e);
        }
    }
}
