# 实时指标页面新功能测试指南

## 测试环境准备

### 1. 启动开发服务器
```bash
cd front_server
npm run dev
```

### 2. 访问实时指标页面
- 登录系统后，进入终端管理页面
- 点击任意终端的"查看指标"按钮
- 或直接访问：`http://localhost:5173/real-time-metrics/{设备MAC地址}`

## 功能测试清单

### 一、自定义时间范围查询功能

#### 测试步骤：
1. **打开自定义时间选择器**
   - 在图表控制区域找到"📅 自定义时间"按钮
   - 点击按钮，应该弹出时间范围选择对话框

2. **测试时间输入**
   - 在"开始时间"输入框中选择一个过去的时间
   - 在"结束时间"输入框中选择一个较晚的时间
   - 验证时间选择器的约束（开始时间不能晚于结束时间）

3. **测试快速预设**
   - 点击"最近30分钟"按钮，验证时间是否自动填充
   - 点击"最近2小时"按钮，验证时间是否正确更新
   - 点击"今天"按钮，验证是否设置为今天0点到当前时间
   - 点击"昨天"按钮，验证是否设置为昨天全天

4. **应用自定义时间范围**
   - 设置好时间范围后，点击"应用"按钮
   - 观察图表是否重新加载数据
   - 检查图表标题是否显示自定义的时间范围
   - 验证数据点数量是否符合预期

#### 预期结果：
- 弹框正常显示和关闭
- 时间输入验证正确
- 快速预设功能正常
- 图表数据正确更新
- 时间范围信息正确显示

### 二、图表缩放功能

#### 测试步骤：
1. **鼠标滚轮缩放**
   - 将鼠标悬停在图表区域
   - 向上滚动鼠标滚轮进行放大
   - 向下滚动鼠标滚轮进行缩小
   - 验证缩放是否平滑

2. **拖拽移动**
   - 在缩放状态下，按住鼠标左键拖拽
   - 验证是否可以查看不同时间段的数据
   - 检查拖拽是否流畅

3. **滑动条控制**
   - 查看图表底部是否显示滑动条（数据量大时）
   - 拖拽滑动条的手柄调整显示范围
   - 验证滑动条与图表显示的同步性

4. **重置缩放**
   - 在缩放状态下，点击"🔍 重置缩放"按钮
   - 验证图表是否恢复到完整视图
   - 检查按钮状态是否正确更新

#### 预期结果：
- 鼠标滚轮缩放功能正常
- 拖拽移动功能正常
- 滑动条显示和控制正常
- 重置缩放功能正常
- 缩放状态跟踪正确

### 三、界面交互测试

#### 测试步骤：
1. **时间范围切换**
   - 先使用自定义时间范围
   - 然后点击预设时间范围按钮（如"最近1小时"）
   - 验证是否正确切换回预设模式

2. **响应式设计**
   - 调整浏览器窗口大小
   - 验证自定义时间弹框在小屏幕上的显示
   - 检查按钮布局是否适应不同屏幕尺寸

3. **错误处理**
   - 尝试设置无效的时间范围（开始时间晚于结束时间）
   - 验证错误提示是否正确显示
   - 测试网络错误情况下的处理

#### 预期结果：
- 时间范围切换正常
- 响应式设计正确
- 错误处理机制有效

## Mock数据测试

如果使用Mock数据模式（`VITE_USE_MOCK=true`），系统会生成模拟数据：

### 验证Mock数据：
1. 检查自定义时间范围是否生成对应时间段的数据
2. 验证数据点的时间间隔是否为5分钟
3. 确认数据值是否在合理范围内

## 常见问题排查

### 1. 自定义时间弹框不显示
- 检查控制台是否有JavaScript错误
- 验证Vue组件的响应式数据是否正确

### 2. 图表缩放不工作
- 确认ECharts版本是否支持dataZoom
- 检查图表配置是否正确

### 3. 时间范围数据不正确
- 检查API调用参数是否正确
- 验证时间格式转换是否正确

### 4. 性能问题
- 大时间范围查询可能较慢，属于正常现象
- 可以通过缩小时间范围来提高响应速度

## 测试数据建议

### 推荐的测试时间范围：
1. **短时间范围**：最近30分钟 - 用于测试基本功能
2. **中等时间范围**：最近2小时 - 用于测试缩放功能
3. **长时间范围**：最近24小时 - 用于测试性能和大数据量处理
4. **历史数据**：昨天全天 - 用于测试历史数据查询

### 测试设备建议：
- 选择有较多历史数据的设备进行测试
- 如果是新设备，可以等待一段时间积累数据后再测试

## 验收标准

### 功能完整性：
- ✅ 自定义时间范围选择器正常工作
- ✅ 快速时间预设功能正常
- ✅ 图表缩放功能完整
- ✅ 重置缩放功能正常

### 用户体验：
- ✅ 界面响应迅速
- ✅ 操作反馈及时
- ✅ 错误提示清晰
- ✅ 移动端适配良好

### 数据准确性：
- ✅ 时间范围查询结果正确
- ✅ 缩放显示数据准确
- ✅ 时间信息显示正确

## 报告问题

如果在测试过程中发现问题，请记录：
1. 问题描述
2. 重现步骤
3. 预期结果 vs 实际结果
4. 浏览器和版本信息
5. 控制台错误信息（如有）
