package com.unnet.changan5G.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.unnet.changan5G.dto.MetricRequestBody;
import com.unnet.changan5G.dto.metric.DiskUsageInfo;
import com.unnet.changan5G.dto.terminal.TerminalAlertInfo;
import com.unnet.changan5G.dto.terminal.TerminalBasicInfo;
import com.unnet.changan5G.dto.terminal.TerminalMetricInfo;
import com.unnet.changan5G.entity.MetricThresholdConfigEntity;
import com.unnet.changan5G.util.MetricJsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 指标数据分类处理服务
 * 
 * <AUTHOR>
 * @date 2024-07-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MetricDataClassificationService {

    /**
     * 提取终端指标信息
     */
    public TerminalMetricInfo extractTerminalMetricInfo(MetricRequestBody metricData, String formattedMac, LocalDateTime metricCollectTime, LocalDateTime receiveTime) {
        TerminalMetricInfo metricInfo = new TerminalMetricInfo();
        metricInfo.setVin(metricData.getVin());
        metricInfo.setIdentityMac(formattedMac); // 使用格式化的MAC地址作为设备标识
        metricInfo.setUptime(metricData.getUptime());
        metricInfo.setCpuTemp(metricData.getCpuTemp());
        metricInfo.setTemperatures(metricData.getTemperatures());
        metricInfo.setCpuUsage(metricData.getCpuUsage());
        metricInfo.setCpuPercent(metricData.getCpuPercent());
        metricInfo.setMemoryPercent(metricData.getMemoryPercent());
        metricInfo.setMemoryUsage(metricData.getMemoryUsage());
        metricInfo.setDiskUsage(metricData.getDiskUsage());
        metricInfo.setDiskDataPercent(metricData.getDiskDataPercent());
        metricInfo.setDiskSystemPercent(metricData.getDiskSystemPercent());
        metricInfo.setCdata(metricData.getCdata());
        metricInfo.setZdata(metricData.getZdata());
        metricInfo.setGroupUsage(metricData.getGroupUsage());
        metricInfo.setGroupBps(metricData.getGroupBps());
        metricInfo.setMetricTime(metricCollectTime);  // 使用传入的指标采集时间
        metricInfo.setReceiveTime(receiveTime);       // 使用传入的接收时间

        log.info("提取终端指标信息完成 - 设备MAC: {}, CPU温度: {}°C, CPU使用率: {}%, 内存使用率: {}%, 采集时间: {}",
                metricInfo.getIdentityMac(), metricInfo.getCpuTemp(),
                metricInfo.getCpuPercent(), metricInfo.getMemoryPercent(), metricCollectTime);

        return metricInfo;
    }
}
