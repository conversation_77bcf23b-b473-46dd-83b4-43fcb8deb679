# 实时指标页面功能增强

## 概述

本次更新为实时指标页面添加了两个重要功能：
1. **自定义时间范围查询** - 允许用户选择具体的开始和结束时间来查询指标数据
2. **图表缩放功能** - 允许用户缩放查看某一时间段的详细数据

## 新增功能详情

### 1. 自定义时间范围查询

#### 功能描述
- 用户可以通过点击"自定义时间"按钮打开时间范围选择器
- 支持精确到分钟的时间选择
- 提供快速时间预设选项（最近30分钟、最近2小时、今天、昨天等）
- 自动验证时间范围的有效性

#### 使用方法
1. 点击图表控制区域的"📅 自定义时间"按钮
2. 在弹出的对话框中选择开始时间和结束时间
3. 或者点击快速预设按钮快速选择常用时间范围
4. 点击"应用"按钮查询数据

#### 技术实现
- 新增 `customTimeRangeVisible` 状态控制弹框显示
- 新增 `fetchHistoryDataByCustomTimeRange` 函数处理自定义时间范围数据获取
- 新增 `getHistoryMetricsByCustomTimeRange` API方法
- 支持Mock数据和真实API两种模式

### 2. 图表缩放功能

#### 功能描述
- 支持鼠标滚轮缩放图表
- 支持拖拽移动查看不同时间段
- 提供滑动条进行精确的范围选择
- 一键重置缩放功能

#### 使用方法
1. **鼠标滚轮缩放**：在图表区域使用鼠标滚轮进行缩放
2. **拖拽移动**：按住鼠标左键拖拽查看不同时间段
3. **滑动条选择**：使用图表底部的滑动条精确选择时间范围
4. **重置缩放**：点击"🔍 重置缩放"按钮恢复到完整视图

#### 技术实现
- 启用ECharts的dataZoom组件
- 添加inside和slider两种缩放方式
- 新增 `isZoomed` 状态跟踪缩放状态
- 新增 `resetZoom` 函数重置缩放

## 界面改进

### 时间范围显示
- 图表标题区域显示当前查询的时间范围
- 自定义时间范围显示完整的时间区间
- 预设时间范围显示范围名称

### 控制按钮优化
- 重新设计了图表控制区域布局
- 添加了缩放控制按钮组
- 优化了按钮的视觉效果和交互反馈

### 响应式设计
- 移动端优化了时间选择器布局
- 调整了按钮大小和间距
- 优化了弹框在小屏幕上的显示

## API接口

### 新增接口

#### getHistoryMetricsByCustomTimeRange
```javascript
// 根据自定义时间范围获取历史指标数据
async getHistoryMetricsByCustomTimeRange(identityMac, params)
```

**参数：**
- `identityMac`: 设备MAC地址
- `params.startTime`: 开始时间（ISO格式）
- `params.endTime`: 结束时间（ISO格式）

**返回：**
```javascript
{
  content: [...], // 指标数据数组
  totalElements: 1000, // 总数据量
  totalPages: 1,
  size: 1000,
  number: 0
}
```

## 配置选项

### 时间范围预设
可以通过修改 `quickTimePresets` 数组来自定义快速时间预设选项：

```javascript
const quickTimePresets = ref([
  {
    key: 'last30min',
    label: '最近30分钟',
    getRange: () => {
      const end = new Date()
      const start = new Date(end.getTime() - 30 * 60 * 1000)
      return { start, end }
    }
  },
  // ... 更多预设
])
```

### 缩放配置
可以通过修改dataZoom配置来调整缩放行为：

```javascript
dataZoom: [
  {
    type: 'inside',
    zoomOnMouseWheel: true, // 鼠标滚轮缩放
    moveOnMouseMove: true,  // 鼠标移动
    moveOnMouseWheel: true  // 鼠标滚轮移动
  },
  {
    type: 'slider',
    show: true, // 显示滑动条
    height: 30  // 滑动条高度
  }
]
```

## 兼容性

- 支持现有的预设时间范围功能
- 兼容Mock数据和真实API
- 保持原有的实时数据更新功能
- 向后兼容现有的URL参数

## 使用示例

### 查询昨天的数据
1. 点击"自定义时间"按钮
2. 点击"昨天"快速预设
3. 点击"应用"按钮

### 缩放查看特定时间段
1. 使用鼠标滚轮在图表上缩放到感兴趣的时间段
2. 拖拽图表查看不同时间点的数据
3. 使用底部滑动条进行精确调整
4. 点击"重置缩放"返回完整视图

## 注意事项

1. 自定义时间范围不能超过当前时间
2. 开始时间必须早于结束时间
3. 大时间范围查询可能需要较长加载时间
4. 缩放功能在数据点较少时可能不明显
5. Mock模式下会生成模拟数据用于测试
