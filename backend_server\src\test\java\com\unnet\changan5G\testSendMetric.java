package com.unnet.changan5G;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@SpringBootTest
public class testSendMetric {

    private static final String BASE_URL = "http://localhost:8081/api/agent/report";
    private static final RestTemplate restTemplate = new RestTemplate();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Random random = new Random();
    
    // 基础数据
    private double baseUptime = 0.0;
    private double baseCpuTemp = 46.23;
    private long baseGroupBps = 5385836L;
    private double baseCpuPercent = 28.2;
    private double baseMemoryPercent = 35.7;
    private int baseSmartmonTemp = 49;
    private int baseSmartmonPercentageUsed = 9;
    private long baseDataUnitsWritten = 143996158L;

    @Test
    public void testSendMetricData() throws Exception {
//        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
        
        System.out.println("开始发送指标数据，每15秒发送一次...");
        
//        scheduler.scheduleAtFixedRate(() -> {
//            try {
//                sendMetricData();
//            } catch (Exception e) {
//                System.err.println("发送指标数据失败: " + e.getMessage());
//                e.printStackTrace();
//            }
//        }, 0, 15, TimeUnit.SECONDS);
//
//        // 运行5分钟后停止
//        Thread.sleep(300000);
        sendMetricData();
//        scheduler.shutdown();
        System.out.println("测试结束");
    }

    private void sendMetricData() throws Exception {
        // 更新模拟数据
        updateSimulatedValues();
        
        // 构建指标数据
        Map<String, Object> metricData = buildMetricData();
        
        // 转换为JSON字符串
        String jsonData = objectMapper.writeValueAsString(metricData);
        
        // 发送HTTP请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-API-Key", "api-SvardHNxjL87TMHbgS2JFDpUsyi1iq9e");
        
        HttpEntity<String> request = new HttpEntity<>(jsonData, headers);
        
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(BASE_URL, request, String.class);
            System.out.println("发送成功 - 状态码: " + response.getStatusCode() + 
                             ", uptime: " + baseUptime + 
                             ", cpu_temp: " + baseCpuTemp + 
                             ", group_bps: " + baseGroupBps + 
                             ", cpu_percent: " + baseCpuPercent +
                             ", memory_percent: " + baseMemoryPercent +
                             ", smartmon_temp: " + baseSmartmonTemp +
                             ", smartmon_percentage_used: " + baseSmartmonPercentageUsed +
                             ", data_units_written: " + baseDataUnitsWritten);
        } catch (Exception e) {
            System.err.println("发送失败: " + e.getMessage());
        }
    }

    private void updateSimulatedValues() {
        // uptime每次增加15秒
        baseUptime += 15.0;
        
        // cpu_temp变化范围不超过10度
        double tempChange = (random.nextDouble() - 0.5) * 2; // -1 到 1 之间
        baseCpuTemp += tempChange;
        baseCpuTemp = Math.max(36.0, Math.min(56.0, baseCpuTemp)); // 限制在36-56度之间
        
        // group_bps变化范围不超过1000
        long bpsChange = (long)((random.nextDouble() - 0.5) * 2000); // -1000 到 1000 之间
        baseGroupBps += bpsChange;
        baseGroupBps = Math.max(4385836L, Math.min(6385836L, baseGroupBps)); // 限制范围
        
        // cpu_percent变化范围不超过10
        double percentChange = (random.nextDouble() - 0.5) * 20; // -10 到 10 之间
        baseCpuPercent += percentChange;
        baseCpuPercent = Math.max(18.2, Math.min(38.2, baseCpuPercent)); // 限制范围
        
        // memory_percent变化范围不超过5%
        double memoryChange = (random.nextDouble() - 0.5) * 10; // -5 到 5 之间
        baseMemoryPercent += memoryChange;
        baseMemoryPercent = Math.max(30.7, Math.min(40.7, baseMemoryPercent)); // 限制在30.7-40.7之间
        
        // smartmon temperature变化范围不超过10度
        int smartmonTempChange = (int)((random.nextDouble() - 0.5) * 20); // -10 到 10 之间
        baseSmartmonTemp += smartmonTempChange;
        baseSmartmonTemp = Math.max(39, Math.min(59, baseSmartmonTemp)); // 限制在39-59度之间
        
        // smartmon percentage_used变化范围不超过5%
        int percentageUsedChange = (int)((random.nextDouble() - 0.5) * 10); // -5 到 5 之间
        baseSmartmonPercentageUsed += percentageUsedChange;
        baseSmartmonPercentageUsed = Math.max(4, Math.min(14, baseSmartmonPercentageUsed)); // 限制在4-14之间
        
        // data_units_written变化范围不超过10000
        long dataUnitsChange = (long)((random.nextDouble() - 0.5) * 20000); // -10000 到 10000 之间
        baseDataUnitsWritten += dataUnitsChange;
        baseDataUnitsWritten = Math.max(143986158L, Math.min(144006158L, baseDataUnitsWritten)); // 限制范围
    }

    private Map<String, Object> buildMetricData() {
        Map<String, Object> data = new HashMap<>();
        
        data.put("hostname", "ARM3588_16034");
        data.put("vin", "ef124ff1234");
        data.put("device_id", "cbca3e75aded4d659f794ad7b367e14f");
        data.put("identity_mac", "12e5c3d1f6e5");
        data.put("uptime", round(baseUptime, 2));
        data.put("cpu_temp", round(baseCpuTemp, 2));
        
        // temperatures
        Map<String, Double> temperatures = new HashMap<>();
        temperatures.put("bigcore1-thermal", round(baseCpuTemp, 2));
        temperatures.put("soc-thermal", round(baseCpuTemp, 2));
        temperatures.put("gpu-thermal", round(baseCpuTemp - 0.923, 3));
        temperatures.put("littlecore-thermal", round(baseCpuTemp, 2));
        temperatures.put("bigcore0-thermal", round(baseCpuTemp - 0.923, 3));
        temperatures.put("npu-thermal", round(baseCpuTemp - 0.923, 3));
        temperatures.put("center-thermal", round(baseCpuTemp, 2));
        data.put("temperatures", temperatures);
        
        // cpu_usage - 基于baseCpuPercent动态计算
        Map<String, Object> cpuUsage = new HashMap<>();
        cpuUsage.put("num", "8");
        cpuUsage.put("core", "8");
        cpuUsage.put("thread", "8");
        
        // 基于baseCpuPercent动态分配user、sys、idle
        double userPercent = baseCpuPercent * 0.67 + random.nextGaussian() * 2; // 约67%的CPU使用率来自user
        double sysPercent = baseCpuPercent * 0.21 + random.nextGaussian() * 1; // 约21%来自sys
        double idlePercent = 100 - baseCpuPercent + random.nextGaussian() * 2; // 剩余为idle
        
        cpuUsage.put("user", String.valueOf(round(Math.max(0, userPercent), 1)));
        cpuUsage.put("sys", String.valueOf(round(Math.max(0, sysPercent), 1)));
        cpuUsage.put("idle", String.valueOf(round(Math.max(0, idlePercent), 1)));
        data.put("cpu_usage", cpuUsage);
        
        data.put("cpu_percent", round(baseCpuPercent, 1));
        data.put("memory_percent", round(baseMemoryPercent, 1));
        
        // memory_usage - 基于baseMemoryPercent动态计算
        Map<String, Object> memoryUsage = new HashMap<>();
        long totalMemory = 16719081472L;
        long usedMemory = (long)(totalMemory * baseMemoryPercent / 100);
        long availableMemory = totalMemory - usedMemory;
        
        memoryUsage.put("total", totalMemory);
        memoryUsage.put("available", availableMemory);
        memoryUsage.put("percent", round(baseMemoryPercent, 1));
        memoryUsage.put("used", usedMemory);
        memoryUsage.put("free", availableMemory / 2);
        memoryUsage.put("active", usedMemory + random.nextInt(1000000000));
        memoryUsage.put("inactive", availableMemory + random.nextInt(1000000000));
        memoryUsage.put("buffers", 33615872L + random.nextInt(10000000));
        memoryUsage.put("cached", availableMemory / 2);
        memoryUsage.put("shared", 158978048L + random.nextInt(50000000));
        memoryUsage.put("slab", 271261696L + random.nextInt(100000000));
        data.put("memory_usage", memoryUsage);
        
        // disk_usage
        List<Map<String, Object>> diskUsage = new ArrayList<>();
        
        // 第一个磁盘 - 系统磁盘，添加动态变化
        Map<String, Object> disk1 = new HashMap<>();
        disk1.put("device", "/dev/mmcblk0p6");
        disk1.put("mountpoint", "/");
        disk1.put("fstype", "ext4");
        long disk1Total = 245941342208L;
        long disk1Used = 11803267072L + random.nextInt(1000000000); // 增加随机变化
        long disk1Free = disk1Total - disk1Used;
        double disk1Percent = (double)disk1Used / disk1Total * 100;
        
        disk1.put("total", disk1Total);
        disk1.put("used", disk1Used);
        disk1.put("free", disk1Free);
        disk1.put("percent", round(disk1Percent, 1));
        disk1.put("smartmon", new HashMap<>());
        
        Map<String, Object> ioRate1 = new HashMap<>();
        ioRate1.put("MBr/s", round(random.nextDouble() * 10, 1)); // 0-10 MB/s
        ioRate1.put("MBw/s", round(random.nextDouble() * 5, 1)); // 0-5 MB/s
        ioRate1.put("read/s", random.nextInt(100)); // 0-99 reads/s
        ioRate1.put("write/s", random.nextInt(50)); // 0-49 writes/s
        disk1.put("io_rate", ioRate1);
        
        diskUsage.add(disk1);
        
        // 第二个磁盘 - 数据磁盘，添加动态变化
        Map<String, Object> disk2 = new HashMap<>();
        disk2.put("device", "/dev/nvme0n1p1");
        disk2.put("mountpoint", "/mnt/nvme0n1p1");
        disk2.put("fstype", "ext4");
        long disk2Total = 983350091776L;
        long disk2Used = 15583633408L + random.nextInt(1000000000); // 增加随机变化
        long disk2Free = disk2Total - disk2Used;
        double disk2Percent = (double)disk2Used / disk2Total * 100;
        
        disk2.put("total", disk2Total);
        disk2.put("used", disk2Used);
        disk2.put("free", disk2Free);
        disk2.put("percent", round(disk2Percent, 1));
        
        // smartmon数据 - 使用动态变化的值
        Map<String, Object> smartmon = new HashMap<>();
        smartmon.put("critical_warning", 0);
        smartmon.put("temperature", baseSmartmonTemp);
        smartmon.put("available_spare", 100);
        smartmon.put("available_spare_threshold", 10);
        smartmon.put("percentage_used", baseSmartmonPercentageUsed);
        smartmon.put("data_units_read", 113016600L + random.nextInt(1000000));
        smartmon.put("data_units_written", baseDataUnitsWritten);
        smartmon.put("host_reads", 388949179L + random.nextInt(10000));
        smartmon.put("host_writes", 175354762L + random.nextInt(10000));
        smartmon.put("controller_busy_time", 7964L + random.nextInt(100));
        smartmon.put("power_cycles", 120);
        smartmon.put("power_on_hours", 1080 + (int)(baseUptime / 3600)); // 基于uptime计算
        smartmon.put("unsafe_shutdowns", 80);
        smartmon.put("media_errors", 0);
        smartmon.put("num_err_log_entries", 0);
        smartmon.put("warning_temp_time", 1754);
        smartmon.put("critical_comp_time", 772);
        smartmon.put("temperature_sensors", Arrays.asList(baseSmartmonTemp + random.nextInt(6) - 3, baseSmartmonTemp + random.nextInt(6) - 3));
        smartmon.put("power_on_time", 1080 + (int)(baseUptime / 3600));
        disk2.put("smartmon", smartmon);
        
        Map<String, Object> ioRate2 = new HashMap<>();
        ioRate2.put("MBr/s", round(random.nextDouble() * 20, 1)); // 0-20 MB/s
        ioRate2.put("MBw/s", round(random.nextDouble() * 15, 1)); // 0-15 MB/s
        ioRate2.put("read/s", random.nextInt(200)); // 0-199 reads/s
        ioRate2.put("write/s", random.nextInt(100)); // 0-99 writes/s
        disk2.put("io_rate", ioRate2);
        
        diskUsage.add(disk2);
        
        data.put("disk_usage", diskUsage);
        data.put("disk_data_percent", round(disk2Percent, 1)); // 使用动态计算的值
        data.put("disk_system_percent", round(disk1Percent, 1)); // 使用动态计算的值
        
        // cdata - 添加小幅动态变化
        Map<String, Object> cdata = new HashMap<>();
        cdata.put("count", 1 + random.nextInt(3)); // 1-3之间
        cdata.put("size", 697724846L + random.nextInt(100000000)); // 增加随机变化
        data.put("cdata", cdata);
        
        // zdata - 添加小幅动态变化
        Map<String, Object> zdata = new HashMap<>();
        zdata.put("count", 9 + random.nextInt(5)); // 9-13之间
        zdata.put("size", 13811916993L + random.nextInt(1000000000)); // 增加随机变化
        data.put("zdata", zdata);
        
        // group_usage - 基于baseGroupBps动态分配
        Map<String, Object> groupUsage = new HashMap<>();
        groupUsage.put("group_id", 105);
        
        // 基于baseGroupBps动态计算pps_total
        double basePpsTotal = 1994.0;
        double ppsVariation = (baseGroupBps - 5385836L) / 5385836.0 * basePpsTotal; // 按比例调整
        double currentPpsTotal = basePpsTotal + ppsVariation;
        groupUsage.put("pps_total", round(Math.max(1000.0, currentPpsTotal), 1));
        groupUsage.put("bps_total", (double)baseGroupBps);
        
        // 动态分配buckets的bps值，保持原有比例
        List<Map<String, Object>> buckets = new ArrayList<>();
        double[] bpsRatios = {0.257, 0.002, 0.5, 0.241}; // 原始比例：1386720/5385836, 11312/5385836, 2691806/5385836, 1295998/5385836
        double[] ppsRatios = {0.261, 0.004, 0.496, 0.239}; // 原始比例：520/1994, 8/1994, 989/1994, 477/1994
        
        for (int i = 0; i < 4; i++) {
            Map<String, Object> bucket = new HashMap<>();
            bucket.put("bucket", i);
            bucket.put("conn", 9);
            bucket.put("pps", round(currentPpsTotal * ppsRatios[i], 1));
            bucket.put("bps", round(baseGroupBps * bpsRatios[i], 1));
            buckets.add(bucket);
        }
        
        groupUsage.put("buckets", buckets);
        data.put("group_usage", groupUsage);
        data.put("group_bps", baseGroupBps);
        
        // app_version
        Map<String, String> appVersion = new HashMap<>();
        appVersion.put("controller", "1.0.48");
        appVersion.put("detector", "1.0.48");
        appVersion.put("web", "1.0.55");
        appVersion.put("dist", "1.0.40");
        appVersion.put("extension", "1.0.383");
        appVersion.put("site-packages", "1.0.2");
        appVersion.put("edge-file-uploader", "2.3.1");
        data.put("app_version", appVersion);
        
        data.put("expired_date", "2026-06-15 10:47:08");
        
        return data;
    }
    
    private double round(double value, int places) {
        if (places < 0) throw new IllegalArgumentException();
        
        BigDecimal bd = BigDecimal.valueOf(value);
        bd = bd.setScale(places, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }
}
